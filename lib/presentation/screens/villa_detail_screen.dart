import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

import '../../data/models/villa_model.dart';
import '../../data/models/api_villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../routes.dart';
import './calendar_screen.dart'; // Import for CalendarScreen

class VillaDetailScreen extends StatefulWidget {
  final String villaId;

  const VillaDetailScreen({super.key, required this.villaId});

  @override
  State<VillaDetailScreen> createState() => _VillaDetailScreenState();
}

class _VillaDetailScreenState extends State<VillaDetailScreen> {
  int _currentImageIndex = 0;
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  bool _isVideoPlaying = false;
  final List<Map<String, dynamic>> _mediaItems = [];

  // Google Maps controller
  final Completer<GoogleMapController> _mapController = Completer();
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    // Fetch villa details when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Provider.of<VillaProvider>(context, listen: false)
            .fetchVillaDetailsById(widget.villaId);
      }
    });
  }

  @override
  void dispose() {
    _disposeMapController();
    _disposeVideoController();
    super.dispose();
  }

  Future<void> _disposeMapController() async {
    // Dispose the GoogleMapController when the widget is disposed.
    if (_mapController.isCompleted) {
      try {
        final GoogleMapController controller = await _mapController.future;
        controller.dispose();
      } catch (e) {
        // Handle or log error if controller disposal fails
        print('Error disposing map controller: $e');
      }
    }
  }

  void _disposeVideoController() {
    if (_videoController != null) {
      _videoController!.dispose();
      _videoController = null;
    }
  }

  Future<void> _initializeVideo(String videoUrl) async {
    try {
      _disposeVideoController(); // Dispose previous controller if any
      _videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      
      await _videoController!.initialize();
      
      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
          _isVideoPlaying = false;
        });
      }

      // Add listener for video completion
      _videoController!.addListener(() {
        if (mounted && _videoController!.value.position >= _videoController!.value.duration) {
          setState(() {
            _isVideoPlaying = false;
          });
        }
      });
    } catch (e) {
      print('Error initializing video: $e');
      if (mounted) {
        setState(() {
          _isVideoInitialized = false;
          _isVideoPlaying = false;
        });
      }
    }
  }

  void _toggleVideoPlayback() {
    if (_videoController != null && _isVideoInitialized) {
      setState(() {
        if (_isVideoPlaying) {
          _videoController!.pause();
          _isVideoPlaying = false;
        } else {
          _videoController!.play();
          _isVideoPlaying = true;
        }
      });
    }
  }

  void _onMediaItemTapped(int index) {
    // Dispose current video if switching away from video
    if (_currentImageIndex < _mediaItems.length &&
        _mediaItems[_currentImageIndex]['type'] == 'video') {
      _disposeVideoController();
      setState(() {
        _isVideoInitialized = false;
        _isVideoPlaying = false;
      });
    }

    setState(() {
      _currentImageIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VillaProvider>(
      builder: (context, villaProvider, child) {
        // Use the new state variables for loading, error, and villa detail
        if (villaProvider.isVillaDetailLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (villaProvider.villaDetailError != null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(villaProvider.villaDetailError!),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      villaProvider.fetchVillaDetailsById(widget.villaId);
                    },
                    child: const Text('Retry'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        final apiVillaData = villaProvider.currentVillaDetail;
        Villa? villaData; // This will be derived from apiVillaData

        if (apiVillaData == null) {
          // This case should ideally be handled by the error state or if fetch hasn't completed
          // Or, if fetch was successful but returned null (e.g. villa not found by API)
          return Scaffold(
            appBar: AppBar(
              title: const Text('Villa Not Found'),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Villa details could not be loaded.',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please try again or go back.',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                       villaProvider.fetchVillaDetailsById(widget.villaId);
                    },
                    child: const Text('Retry Fetch'),
                  ),
                  const SizedBox(height: 12),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }
        
        // If apiVillaData is available, convert it to Villa for UI compatibility
        // The UI primarily uses ApiVilla fields now, but Villa can be a bridge/fallback
        villaData = villaProvider.convertApiVillaToVilla(apiVillaData);
        if (villaData == null) {
            // This means conversion failed, which is unlikely if apiVillaData is valid
            // but handle it as an error.
             return Scaffold(
                appBar: AppBar(title: const Text('Data Error')),
                body: Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                    const Text('Could not process villa data.'),
                    const SizedBox(height: 20),
                    ElevatedButton(
                        onPressed: () {
                        villaProvider.fetchVillaDetailsById(widget.villaId);
                        },
                        child: const Text('Retry'),
                    ),
                    TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Go Back'),
                    ),
                    ],
                ),
                ),
            );
        }

        return _buildVillaDetailContent(context, villaData, apiVillaData);
      },
    );
  }

  Widget _buildVillaDetailContent(BuildContext context, Villa villa, ApiVilla apiVilla) {
    // Use data primarily from apiVilla, fallback to villa if necessary
    final String displayAddress = apiVilla.address.isNotEmpty ? apiVilla.address : (villa.location.isNotEmpty ? villa.location : apiVilla.area);
    final double displayPrice = apiVilla.effectiveWeekdayPrice; // Or another relevant price from ApiVilla
    final String displayDescription = apiVilla.desc.isNotEmpty ? apiVilla.desc : (villa.description ?? 'No description available');

    // Use amenities from ApiVilla, converted to string list by convertApiVillaToVilla
    final List<String> amenities = villa.amenities ?? [];


    _mediaItems.clear();
    
    // Add profile picture from ApiVilla if available
    if (apiVilla.profilePicture.isNotEmpty) {
      _mediaItems.add({'type': 'image', 'url': apiVilla.profilePicture});
    }
    
    // Add primary image from ApiVilla if different and not already added
    if (apiVilla.primaryImage.isNotEmpty && !_mediaItems.any((item) => item['url'] == apiVilla.primaryImage)) {
      _mediaItems.add({'type': 'image', 'url': apiVilla.primaryImage});
    }

    // Add additional images from ApiVilla
    for (String img in apiVilla.images) {
      if (img.isNotEmpty && !_mediaItems.any((item) => item['url'] == img)) {
        _mediaItems.add({'type': 'image', 'url': img});
      }
    }
    
    // Add videos from ApiVilla
    for (String videoUrl in apiVilla.video) {
      if (videoUrl.isNotEmpty) {
        _mediaItems.add({'type': 'video', 'url': videoUrl});
      }
    }
    
    // Fallback if no media items from ApiVilla, try Villa model's imageUrl
    if (_mediaItems.isEmpty && villa.imageUrl.isNotEmpty) {
        _mediaItems.add({'type': 'image', 'url': villa.imageUrl});
    }
    if (_mediaItems.isEmpty && villa.additionalImages != null) {
         for (String img in villa.additionalImages!) {
            if (img.isNotEmpty && !_mediaItems.any((item) => item['url'] == img)) {
            _mediaItems.add({'type': 'image', 'url': img});
            }
        }
    }


    // Ensure there's at least one media item for display
    if (_mediaItems.isEmpty) {
      _mediaItems.add({
        'type': 'image',
        'url': 'https://via.placeholder.com/400x250/cccccc/ffffff?text=No+Image'
      });
    }
    
    // Ensure _currentImageIndex is valid
    if (_currentImageIndex >= _mediaItems.length) {
        _currentImageIndex = 0;
    }


    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            CustomScrollView(
              slivers: [
                // App Bar with back button and actions
                SliverAppBar(
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
                    onPressed: () => Navigator.pop(context),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(
                        Icons.share_outlined,
                        color: Colors.black,
                      ),
                      onPressed: () {},
                    ),
                    Consumer<VillaProvider>(
                      builder: (context, villaProvider, child) {
                        final isSaved = villaProvider.isVillaSaved(widget.villaId);
                        return IconButton(
                          icon: Icon(
                            isSaved ? Icons.favorite : Icons.favorite_border,
                            color: isSaved ? Colors.red : Colors.black,
                          ),
                          onPressed: () async {
                            final villa = villaProvider.getVillaByIdExtended(widget.villaId);
                            if (villa != null) {
                              await villaProvider.toggleSavedStatus(villa);
                            }
                          },
                        );
                      },
                    ),
                  ],
                  backgroundColor: Colors.white,
                  elevation: 0,
                  pinned: true,
                  expandedHeight: 0,
                  toolbarHeight: 56,
                  title: const Text(
                    'Villa Details',
                    style: TextStyle(color: Colors.black),
                  ),
                  centerTitle: true,
                ),

                // Main content
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main media display with gallery preview
                      Stack(
                        children: [
                          // Main media display
                          SizedBox(
                            height: 250,
                            width: double.infinity,
                            child: _buildMediaWidget(_mediaItems[_currentImageIndex]),
                          ),

                          // Price tag
                          Positioned(
                            top: 16,
                            right: 16,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.black.withAlpha(178),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '₹${(displayPrice / 1000).toStringAsFixed(0)}K',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          // Media gallery preview
                          Positioned(
                            bottom: 16,
                            left: 16,
                            right: 16,
                            child: SizedBox(
                              height: 60,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _mediaItems.length,
                                itemBuilder: (context, index) => GestureDetector(
                                  onTap: () {
                                    _onMediaItemTapped(index);
                                  },
                                  child: Container(
                                    width: 80,
                                    height: 60,
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: _currentImageIndex == index
                                          ? Border.all(
                                              color: Colors.white,
                                              width: 2,
                                            )
                                          : null,
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Stack(
                                        children: [
                                          _buildThumbnailWidget(_mediaItems[index]),
                                          // Video play icon overlay
                                          if (_mediaItems[index]['type'] == 'video')
                                            const Center(
                                              child: Icon(
                                                Icons.play_circle_filled,
                                                color: Colors.white,
                                                size: 24,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Price and basic info
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '₹${displayPrice.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              displayAddress,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Villa capacity and room details
                            ...[
                            // Capacity section
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Capacity & Accommodation',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Wrap(
                                    spacing: 16,
                                    runSpacing: 8,
                                    children: [
                                      if (apiVilla.noOfMemberFrom > 0 || apiVilla.noOfMemberTo > 0)
                                        _buildCapacityItem(
                                          Icons.people,
                                          '${apiVilla.noOfMemberFrom}-${apiVilla.noOfMemberTo} Guests',
                                        ),
                                      if (apiVilla.noOfRoom > 0)
                                        _buildCapacityItem(
                                          Icons.bedroom_parent,
                                          '${apiVilla.noOfRoom} Rooms',
                                        ),
                                      if (apiVilla.noOfBed > 0)
                                        _buildCapacityItem(
                                          Icons.bed,
                                          '${apiVilla.noOfBed} Beds',
                                        ),
                                      if (apiVilla.noOfwashroom > 0)
                                        _buildCapacityItem(
                                          Icons.bathroom,
                                          '${apiVilla.noOfwashroom} Bathrooms',
                                        ),
                                      if (apiVilla.noOfLivingRoom > 0)
                                        _buildCapacityItem(
                                          Icons.chair,
                                          '${apiVilla.noOfLivingRoom} Living Rooms',
                                        ),
                                      if (apiVilla.noOfAc > 0)
                                        _buildCapacityItem(
                                          Icons.ac_unit,
                                          '${apiVilla.noOfAc} AC Units',
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Pricing section
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.blue[50],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Pricing Details',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Weekday',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            '₹${apiVilla.effectiveWeekdayPrice.toStringAsFixed(0)}',
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          if (apiVilla.weekDayDiscountPrice > 0)
                                            Text(
                                              '₹${apiVilla.weekDayPrice.toStringAsFixed(0)}',
                                              style: const TextStyle(
                                                decoration: TextDecoration.lineThrough,
                                                color: Colors.grey,
                                                fontSize: 12,
                                              ),
                                            ),
                                        ],
                                      ),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            'Weekend',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 12,
                                            ),
                                          ),
                                          Text(
                                            '₹${apiVilla.effectiveWeekendPrice.toStringAsFixed(0)}',
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          if (apiVilla.weekendDiscountPrice > 0)
                                            Text(
                                              '₹${apiVilla.weekendPrice.toStringAsFixed(0)}',
                                              style: const TextStyle(
                                                decoration: TextDecoration.lineThrough,
                                                color: Colors.grey,
                                                fontSize: 12,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                          ],

                            const Divider(height: 32),

                            // Property title and description
                            Text(
                              villa.name, // Use actual villa name as title
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              displayDescription,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[800],
                                height: 1.5,
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Features & Amenities
                            const Text(
                              'Features & Amenities',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Amenities grid
                            if (amenities.isNotEmpty)
                              GridView.count(
                                crossAxisCount: 3,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                childAspectRatio: 1.5,
                                children: amenities.map((amenity) {
                                  IconData icon;
                                  switch (amenity.toLowerCase()) {
                                    case 'pool':
                                      icon = Icons.pool;
                                      break;
                                    case 'wifi':
                                      icon = Icons.wifi;
                                      break;
                                    case 'ac':
                                    case 'air conditioning':
                                      icon = Icons.ac_unit;
                                      break;
                                    case 'parking':
                                      icon = Icons.local_parking;
                                      break;
                                    default:
                                      icon = Icons.star; // Fallback icon
                                  }
                                  return Column(
                                    children: [
                                      Icon(icon, color: Colors.blue),
                                      const SizedBox(height: 4),
                                      Text(
                                        amenity,
                                        style: const TextStyle(fontSize: 12),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  );
                                }).toList(),
                              )
                            else
                              const Text(
                                'No amenities listed.',
                                style: TextStyle(color: Colors.grey),
                              ),

                            // Additional villa details from API
                            ...[
                            const SizedBox(height: 24),
                            
                            // Contact & Services section
                            const Text(
                              'Services & Contact',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey[200]!),
                              ),
                              child: Column(
                                children: [
                                  if (apiVilla.contactNumber.isNotEmpty)
                                    _buildServiceItem(
                                      Icons.phone,
                                      'Contact',
                                      apiVilla.contactNumber,
                                    ),
                                  if (apiVilla.chef)
                                    _buildServiceItem(
                                      Icons.restaurant,
                                      'Chef Service',
                                      'Available',
                                    ),
                                  if (apiVilla.meals)
                                    _buildServiceItem(
                                      Icons.dining,
                                      'Meals',
                                      'Available',
                                    ),
                                  if (apiVilla.complimentary)
                                    _buildServiceItem(
                                      Icons.card_giftcard,
                                      'Complimentary Services',
                                      'Available',
                                    ),
                                  if (apiVilla.noOFcaretaker > 0)
                                    _buildServiceItem(
                                      Icons.person_pin,
                                      'Caretakers',
                                      '${apiVilla.noOFcaretaker} Available',
                                    ),
                                ],
                              ),
                            ),

                            // Pool details
                            if (apiVilla.isSwimmingPool) ...[
                              const SizedBox(height: 24),
                              const Text(
                                'Swimming Pool',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.blue[50],
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.blue[200]!),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.pool, color: Colors.blue),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Swimming Pool Available',
                                          style: TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    if (apiVilla.swimmingPoolMeasument.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Icon(Icons.straighten, size: 16, color: Colors.grey),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Size: ${apiVilla.swimmingPoolMeasument}',
                                            style: TextStyle(color: Colors.grey[700]),
                                          ),
                                        ],
                                      ),
                                    ],
                                    if (apiVilla.isKidSwimmingPool) ...[
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Icon(Icons.child_friendly, size: 16, color: Colors.green),
                                          const SizedBox(width: 8),
                                          Text(
                                            'Kids pool available',
                                            style: TextStyle(color: Colors.grey[700]),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],

                            // Additional information
                            if (apiVilla.nearby.isNotEmpty || apiVilla.direction.isNotEmpty || apiVilla.other.isNotEmpty) ...[
                              const SizedBox(height: 24),
                              const Text(
                                'Additional Information',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              if (apiVilla.nearby.isNotEmpty) ...[
                                _buildInfoCard('Nearby Attractions', apiVilla.nearby, Icons.place),
                                const SizedBox(height: 12),
                              ],
                              if (apiVilla.direction.isNotEmpty) ...[
                                _buildInfoCard('Directions', apiVilla.direction, Icons.directions),
                                const SizedBox(height: 12),
                              ],
                              if (apiVilla.other.isNotEmpty) ...[
                                _buildInfoCard('Other Details', apiVilla.other, Icons.info),
                              ],
                            ],
                          ],
                          ],
                        ),
                      ),

                      // Location section
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Location',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Interactive Map
                            Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: Colors.grey[200],
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(20),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Stack(
                                  children: [
                                    // Google Map
                                    villa.latitude != null &&
                                            villa.longitude != null
                                        ? GoogleMap(
                                          key: ValueKey('google_map_${villa.id}'), // Add a unique key
                                          initialCameraPosition: CameraPosition(
                                            target: LatLng(
                                              villa.latitude!,
                                              villa.longitude!,
                                            ),
                                            zoom: 14,
                                          ),
                                          onMapCreated: (
                                            GoogleMapController controller,
                                          ) {
                                            if (!_mapController.isCompleted) {
                                              _mapController.complete(
                                                controller,
                                              );
                                            }

                                            // Add marker for villa location
                                            if (mounted) { // Check if widget is still mounted
                                              setState(() {
                                                _markers.clear(); // Clear previous markers
                                                _markers.add(
                                                  Marker(
                                                    markerId: MarkerId(villa.id),
                                                    position: LatLng(
                                                      villa.latitude!,
                                                      villa.longitude!,
                                                    ),
                                                    infoWindow: InfoWindow(
                                                      title: villa.name,
                                                      snippet: villa.location,
                                                    ),
                                                  ),
                                                );
                                              });
                                            }
                                          },
                                          markers: _markers,
                                          zoomControlsEnabled: false,
                                          mapToolbarEnabled: false,
                                          myLocationButtonEnabled: false,
                                        )
                                        : Center(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.map,
                                                size: 40,
                                                color: Colors.grey[400],
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                'Map not available',
                                                style: TextStyle(
                                                  color: Colors.grey[600],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                    // Open in Maps button
                                    if (villa.latitude != null &&
                                        villa.longitude != null)
                                      Positioned(
                                        bottom: 10,
                                        right: 10,
                                        child: ElevatedButton.icon(
                                          onPressed: () async {
                                            final uri = Uri.parse(
                                              'https://www.google.com/maps/search/?api=1&query=${villa.latitude},${villa.longitude}',
                                            );
                                            if (await canLaunchUrl(uri)) {
                                              await launchUrl(
                                                uri,
                                                mode:
                                                    LaunchMode
                                                        .externalApplication,
                                              );
                                            }
                                          },
                                          icon: const Icon(
                                            Icons.directions,
                                            size: 16,
                                          ),
                                          label: const Text('Directions'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.white,
                                            foregroundColor: Colors.blue,
                                            elevation: 2,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            textStyle: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              villa.location, // Use actual villa location description
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[800],
                              ),
                            ),

                            // Add padding at the bottom for the fixed booking button
                            const SizedBox(height: 80),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Fixed booking button at the bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(20),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: () {
                    // Navigate to CalendarScreen
                    Navigator.of(context).pushNamed(
                      CalendarScreen.routeName,
                      arguments: villa.id, // Pass villaId as argument
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Book Now',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.blue),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildCapacityItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.blue),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildServiceItem(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.blue),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, String content, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: Colors.blue),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyFeature(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(text, style: const TextStyle(fontSize: 12)),
    );
  }

  Widget _buildMediaWidget(Map<String, dynamic> mediaItem) {
    if (mediaItem['type'] == 'video') {
      return Container(
        color: Colors.black,
        child: Stack(
          children: [
            // Video player or thumbnail
            if (_videoController != null &&
                _videoController!.value.isInitialized &&
                _isVideoInitialized)
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              )
            else
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.grey[800],
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.videocam,
                        size: 48,
                        color: Colors.white70,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Video Content',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // Video controls overlay
            if (_videoController != null && _isVideoInitialized)
              Center(
                child: GestureDetector(
                  onTap: _toggleVideoPlayback,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isVideoPlaying ? Icons.pause : Icons.play_arrow,
                      size: 48,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
            else
              // Play button for uninitialized video
              Center(
                child: GestureDetector(
                  onTap: () => _initializeVideo(mediaItem['url']),
                  child: const Icon(
                    Icons.play_circle_filled,
                    size: 64,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      );
    } else {
      // For images
      return Image.network(
        mediaItem['url'],
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 40,
                color: Colors.grey,
              ),
            ),
          );
        },
      );
    }
  }

  Widget _buildThumbnailWidget(Map<String, dynamic> mediaItem) {
    if (mediaItem['type'] == 'video') {
      // For video thumbnails, show a dark overlay
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.videocam,
            size: 20,
            color: Colors.white70,
          ),
        ),
      );
    } else {
      // For image thumbnails
      return Image.network(
        mediaItem['url'],
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[300],
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 20,
                color: Colors.grey,
              ),
            ),
          );
        },
      );
    }
  }
}
