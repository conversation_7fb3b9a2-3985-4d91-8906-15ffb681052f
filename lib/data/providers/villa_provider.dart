import 'package:flutter/material.dart';
import '../models/villa_model.dart';
import '../models/api_villa_model.dart';
import '../models/category_model.dart';
import '../models/wishlist_model.dart';
import '../models/amenity_model.dart';
import '../datasources/api_service.dart';
import '../providers/user_provider.dart';
import '../../core/utils/service_locator.dart';
import '../../core/utils/location_service.dart';
import '../../core/utils/date_utils.dart' as app_date_utils;

class VillaProvider extends ChangeNotifier {
  final ApiService _apiService = sl.apiService;
  UserProvider? _userProvider;

  // State management
  bool _isLoading = false;
  bool _isCategoriesLoading = false;
  bool _isWishlistLoading = false;
  String? _error;
  String? _categoriesError;
  String? _wishlistError;

  // Villa Detail
  ApiVilla? _currentVillaDetail;
  bool _isVillaDetailLoading = false;
  String? _villaDetailError;

  // Categories
  List<Category> _categories = [];
  Category? _selectedCategory;

  // Amenities
  List<Amenity> _amenities = [];
  bool _isAmenitiesLoading = false;
  String? _amenitiesError;

  // API Villas from /villa endpoint
  List<ApiVilla> _apiVillas = [];

  // Separate list for map search results to avoid affecting home screen
  List<ApiVilla> _mapSearchResults = [];

  // Wishlist
  List<WishlistItem> _wishlistItems = [];

  // Mock data for featured villas
  List<Villa> _featuredVillas = [
    Villa(
      id: '1',
      name: 'Oceanfront Villa',
      location: 'Miami Beach',
      imageUrl:
          'https://images.unsplash.com/photo-1582610116397-edb318620f90?q=80&w=1000',
      rating: 4.9,
      price: 599,
      isFeatured: true,
      latitude: 25.7907,
      longitude: -80.1300,
      description:
          'Luxurious oceanfront villa with stunning sea views and private beach access.',
      amenities: ['Pool', 'WiFi', 'AC', 'Parking', 'Chef', 'BBQ'],
      additionalImages: [
        'https://images.unsplash.com/photo-1582610116397-edb318620f90?q=80&w=1000',
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
        'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
      ],
    ),
    Villa(
      id: '2',
      name: 'Mountain Retreat',
      location: 'Aspen',
      imageUrl:
          'https://images.unsplash.com/photo-1518780664697-55e3ad937233?q=80&w=1000',
      rating: 4.8,
      price: 549,
      isFeatured: true,
      latitude: 39.1911,
      longitude: -106.8175,
      description:
          'Serene mountain retreat perfect for a peaceful getaway with breathtaking alpine views.',
      amenities: ['WiFi', 'AC', 'Parking', 'Kitchen', 'Fireplace'],
      additionalImages: [
        'https://images.unsplash.com/photo-1518780664697-55e3ad937233?q=80&w=1000',
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1000',
        'https://images.unsplash.com/photo-1582610116397-edb318620f90?q=80&w=1000',
      ],
    ),
  ];

  // Mock data for nearby villas
  List<Villa> _nearbyVillas = [
    Villa(
      id: '3',
      name: 'Modern Luxury Villa',
      location: 'Beverly Hills',
      imageUrl:
          'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
      rating: 4.8,
      price: 399,
      distanceInMiles: 2.5,
      latitude: 34.0736,
      longitude: -118.4004,
      description:
          'Contemporary luxury villa in the heart of Beverly Hills with state-of-the-art amenities.',
      amenities: ['Pool', 'WiFi', 'AC', 'Parking', 'Kitchen', 'Gym'],
      additionalImages: [
        'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
      ],
    ),
    Villa(
      id: '4',
      name: 'Seaside Paradise',
      location: 'Malibu',
      imageUrl:
          'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
      rating: 4.9,
      price: 549,
      distanceInMiles: 3.2,
      latitude: 34.0259,
      longitude: -118.7798,
      description:
          'Beautiful beachfront villa with direct ocean access and spectacular sunset views.',
      amenities: ['Pool', 'WiFi', 'AC', 'Parking', 'BBQ', 'Beach Access'],
      additionalImages: [
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
        'https://images.unsplash.com/photo-1582610116397-edb318620f90?q=80&w=1000',
      ],
    ),
    Villa(
      id: '5',
      name: 'Garden Villa',
      location: 'Santa Barbara',
      imageUrl:
          'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1000',
      rating: 4.7,
      price: 299,
      distanceInMiles: 3.8,
      latitude: 34.4208,
      longitude: -119.6982,
      description:
          'Charming villa surrounded by beautiful gardens and peaceful atmosphere.',
      amenities: ['WiFi', 'AC', 'Parking', 'Kitchen', 'Garden', 'Pet Friendly'],
      additionalImages: [
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1000',
        'https://images.unsplash.com/photo-1518780664697-55e3ad937233?q=80&w=1000',
      ],
    ),
    Villa(
      id: '6',
      name: 'Hillside Mansion',
      location: 'Hollywood Hills',
      imageUrl:
          'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
      rating: 4.6,
      price: 699,
      distanceInMiles: 4.1,
      latitude: 34.1184,
      longitude: -118.3004,
      description:
          'Magnificent hillside mansion with panoramic city views and luxury amenities.',
      amenities: [
        'Pool',
        'WiFi',
        'AC',
        'Parking',
        'Kitchen',
        'Home Theater',
        'Wine Cellar',
      ],
      additionalImages: [
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
        'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
      ],
    ),
  ];

  // Mock data for saved villas
  final List<Villa> _savedVillas = [
    Villa(
      id: '7',
      name: 'Ocean View Estate',
      location: 'Malibu, California',
      imageUrl:
          'https://images.unsplash.com/photo-1518780664697-55e3ad937233?q=80&w=1000',
      rating: 4.8,
      price: 1200,
      isFeatured: true,
      latitude: 34.0259,
      longitude: -118.7798,
      description:
          'Exclusive oceanfront estate with unparalleled luxury and privacy.',
      amenities: [
        'Pool',
        'WiFi',
        'AC',
        'Parking',
        'Private Beach',
        'Chef',
        'Gym',
      ],
      additionalImages: [
        'https://images.unsplash.com/photo-1518780664697-55e3ad937233?q=80&w=1000',
        'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1000',
      ],
    ),
    Villa(
      id: '8',
      name: 'Tuscan Retreat',
      location: 'Florence, Italy',
      imageUrl:
          'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
      rating: 4.7,
      price: 950,
      isFeatured: true,
      latitude: 43.7696,
      longitude: 11.2558,
      description:
          'Authentic Tuscan villa surrounded by vineyards and olive groves.',
      amenities: ['WiFi', 'AC', 'Parking', 'Kitchen', 'Wine Cellar', 'Garden'],
      additionalImages: [
        'https://images.unsplash.com/photo-1613977257363-707ba9348227?q=80&w=1000',
        'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1000',
      ],
    ),
  ];

  // Getters - Prioritize API data over mock data
  List<Villa> get featuredVillas {
    // Convert API villas to Villa format and filter featured ones
    List<Villa> apiConvertedFeatured =
        _apiVillas
            .map((apiVilla) => convertApiVillaToVilla(apiVilla))
            .where(
              (villa) => villa != null && villa.isFeatured,
            ) // Filter for featured villas
            .cast<Villa>()
            .toList();

    // If _apiVillas has been populated (even if it's now empty after filtering), use the filtered list from it.
    // Otherwise (e.g., initial state or API load failed, _apiVillas is empty), fallback to mock _featuredVillas.
    return _apiVillas.isNotEmpty ? apiConvertedFeatured : _featuredVillas;
  }

  List<Villa> get nearbyVillas {
    sl.logger.d(
      'VillaProvider: nearbyVillas getter called. _apiVillas count: ${_apiVillas.length}',
    );

    // Convert API villas to Villa format
    List<Villa> apiConvertedNearby =
        _apiVillas
            .map((apiVilla) => convertApiVillaToVilla(apiVilla))
            .where(
              (villa) => villa != null && !villa.isFeatured,
            ) // Filter for non-featured (nearby) villas
            .cast<Villa>()
            .toList();

    sl.logger.d(
      'VillaProvider: Converted ${apiConvertedNearby.length} nearby villas from API data',
    );

    // If _apiVillas has been populated (even if it's now empty after filtering), use the filtered list from it.
    // Otherwise (e.g., initial state or API load failed, _apiVillas is empty), fallback to mock _nearbyVillas.
    final result = _apiVillas.isNotEmpty ? apiConvertedNearby : _nearbyVillas;
    sl.logger.d(
      'VillaProvider: Returning ${result.length} nearby villas (using ${_apiVillas.isNotEmpty ? "API" : "mock"} data)',
    );

    return result;
  }

  List<Villa> get savedVillas {
    // Convert wishlist items to Villa objects
    if (_wishlistItems.isNotEmpty) {
      return _wishlistItems
          .where((item) => item.villa != null)
          .map((item) => convertApiVillaToVilla(item.villa!))
          .where((villa) => villa != null)
          .cast<Villa>()
          .toList();
    }
    // Return empty list if no wishlist items - no mock data
    return [];
  }

  List<Villa> get allVillas {
    // Prioritize API villas over mock villas
    List<Villa> apiConverted =
        _apiVillas
            .map((apiVilla) => convertApiVillaToVilla(apiVilla))
            .where((villa) => villa != null)
            .cast<Villa>()
            .toList();

    return apiConverted.isNotEmpty
        ? apiConverted
        : [..._featuredVillas, ..._nearbyVillas];
  }

  List<ApiVilla> get apiVillas => _apiVillas;

  // Getter for map search results
  List<ApiVilla> get mapSearchResults => _mapSearchResults;

  // State getters
  bool get isLoading => _isLoading;
  bool get isCategoriesLoading => _isCategoriesLoading;
  bool get isWishlistLoading => _isWishlistLoading;
  String? get error => _error;
  String? get categoriesError => _categoriesError;
  String? get wishlistError => _wishlistError;

  // Villa Detail getters
  ApiVilla? get currentVillaDetail => _currentVillaDetail;
  bool get isVillaDetailLoading => _isVillaDetailLoading;
  String? get villaDetailError => _villaDetailError;

  // Wishlist getters
  List<WishlistItem> get wishlistItems => _wishlistItems;

  // Category getters
  List<Category> get categories => _categories;
  Category? get selectedCategory => _selectedCategory;

  // Amenity getters
  List<Amenity> get amenities => _amenities;
  bool get isAmenitiesLoading => _isAmenitiesLoading;
  String? get amenitiesError => _amenitiesError;

  // Filter villas by category (to be implemented)
  List<Villa> getVillasByCategory(String category) {
    // This would filter villas by category like "Beachfront", "Mountain View", etc.
    return allVillas;
  }

  // Get villa by ID
  Villa? getVillaById(String id) {
    try {
      return allVillas.firstWhere((villa) => villa.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get ApiVilla by ID
  ApiVilla? getApiVillaById(String id) {
    try {
      return _apiVillas.firstWhere((villa) => villa.id.toString() == id);
    } catch (e) {
      return null;
    }
  }

  // Convert ApiVilla to Villa for detail screen compatibility
  Villa? convertApiVillaToVilla(ApiVilla apiVilla) {
    try {
      // Convert boolean amenities to string list
      List<String> amenities = [];

      if (apiVilla.isSwimmingPool) amenities.add('Pool');
      if (apiVilla.wifi) amenities.add('WiFi');
      if (apiVilla.ac) amenities.add('AC');
      if (apiVilla.parking) amenities.add('Parking');
      if (apiVilla.kitchen) amenities.add('Kitchen');
      if (apiVilla.chef) amenities.add('Chef');
      if (apiVilla.barbeque) amenities.add('BBQ');
      if (apiVilla.petFriendly) amenities.add('Pet Friendly');
      if (apiVilla.powerBackup) amenities.add('Power Backup');
      if (apiVilla.fireExtinguisher) amenities.add('Fire Safety');
      if (apiVilla.meals) amenities.add('Meals');
      if (apiVilla.complimentary) amenities.add('Complimentary Services');

      return Villa(
        id: apiVilla.id.toString(),
        name: apiVilla.name,
        location:
            apiVilla.address.isNotEmpty ? apiVilla.address : apiVilla.area,
        imageUrl: apiVilla.primaryImage,
        rating: apiVilla.rating,
        price:
            apiVilla
                .effectiveWeekdayPrice, // Assuming effectiveWeekdayPrice is appropriate
        description:
            apiVilla.desc.isNotEmpty
                ? apiVilla.desc
                : 'Beautiful villa with excellent amenities and services.',
        isFeatured:
            apiVilla.isFeatured, // Use the isFeatured flag from the API model
        latitude: apiVilla.latitude,
        longitude: apiVilla.longitude,
        villaGroupId: apiVilla.villaGroupId > 0 ? apiVilla.villaGroupId : null,
        amenities:
            amenities.isNotEmpty
                ? amenities
                : ['WiFi', 'AC', 'Parking'], // Default amenities if none found
        additionalImages:
            apiVilla.images.isNotEmpty
                ? apiVilla.images
                : [
                  apiVilla.primaryImage.isNotEmpty
                      ? apiVilla.primaryImage
                      : 'https://via.placeholder.com/400x250/cccccc/ffffff?text=No+Image',
                ],
      );
    } catch (e) {
      sl.logger.e('Error converting ApiVilla to Villa: $e');
      return null;
    }
  }

  // Get Villa by ID, prioritizing ApiVillas over mock data
  Villa? getVillaByIdExtended(String id) {
    // First try to find in ApiVillas and convert (prioritize API data)
    ApiVilla? apiVilla = getApiVillaById(id);
    if (apiVilla != null) {
      return convertApiVillaToVilla(apiVilla);
    }

    // Fallback to mock villas only if API villa not found
    Villa? villa = getVillaById(id);
    if (villa != null) return villa;

    return null;
  }

  /// Ensure villa data is available by ID, fetch if necessary
  Future<bool> ensureVillaDataById(String id) async {
    // First check if villa already exists
    if (getApiVillaById(id) != null || getVillaById(id) != null) {
      return true;
    }

    try {
      // Try to fetch all villas if villa not found
      await fetchAllVillas();

      // Check again after fetching
      return getApiVillaById(id) != null || getVillaById(id) != null;
    } catch (e) {
      sl.logger.e('Error ensuring villa data for ID $id: $e');
      return false;
    }
  }

  // Check if a villa is saved in wishlist
  bool isVillaSaved(String id) {
    // First check in wishlist items
    bool isInWishlist = _wishlistItems.any(
      (item) => item.villaId.toString() == id,
    );
    if (isInWishlist) return true;

    // Fallback to mock saved villas
    return _savedVillas.any((villa) => villa.id == id);
  }

  // Toggle saved status
  Future<void> toggleSavedStatus(Villa villa) async {
    if (isVillaSaved(villa.id)) {
      await removeFromWishlist(villa.id);
    } else {
      await addToWishlist(villa.id);
    }
  }

  // API Methods

  /// Fetch dashboard data with location and filter parameters
  Future<void> fetchDashboardData({
    required String lat,
    required String long,
    required int userId,
    required String fromDate,
    required String toDate,
    required int radius,
    int? categoryId, // Made nullable
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      sl.logger.d(
        'VillaProvider: Fetching dashboard data with params: lat=$lat, long=$long, userId=$userId',
      );
      final response = await _apiService.getDashboardData(
        lat: lat,
        long: long,
        userId: userId,
        fromDate: fromDate,
        toDate: toDate,
        radius: radius,
        categoryId: categoryId,
      );

      sl.logger.d('VillaProvider: Dashboard API response: $response');
      sl.logger.d(
        'VillaProvider: Dashboard response type: ${response.runtimeType}',
      );

      if (response != null) {
        List<dynamic>? villaListFromResponse;

        if (response is Map<String, dynamic>) {
          final responseDataField = response['data'];
          if (responseDataField is List) {
            villaListFromResponse = responseDataField;
            sl.logger.d('Extracted villa list from response["data"]');
          } else {
            sl.logger.w(
              'response["data"] is not a List. Type: ${responseDataField?.runtimeType}',
            );
            _error = 'Dashboard data format error: "data" field is not a list.';
          }
        } else {
          sl.logger.w(
            'Unexpected dashboard response type: ${response.runtimeType}. Response: $response',
          );
          _error = 'Unexpected dashboard data structure.';
        }

        if (villaListFromResponse != null) {
          try {
            // Store previous count for comparison
            final previousCount = _apiVillas.length;

            _apiVillas =
                villaListFromResponse
                    .map((json) {
                      try {
                        if (json is! Map<String, dynamic>) {
                          sl.logger.w(
                            'VillaProvider: Villa JSON is not a Map: ${json.runtimeType} - $json',
                          );
                          return null;
                        }
                        return ApiVilla.fromJsonSafe(
                          json,
                        ); // Assuming ApiVilla.fromJsonSafe handles the structure
                      } catch (e, stackTrace) {
                        sl.logger.e(
                          'VillaProvider: Failed to parse villa from dashboard:',
                        );
                        sl.logger.e(
                          ApiVilla.debugJsonData(json),
                        ); // Assuming ApiVilla has this helper
                        sl.logger.e('Error: $e');
                        sl.logger.e('Stack trace: $stackTrace');
                        return null;
                      }
                    })
                    .where((villa) => villa != null)
                    .cast<ApiVilla>()
                    .toList();
            sl.logger.d(
              'VillaProvider: Successfully parsed ${_apiVillas.length} villas from dashboard data (previous: $previousCount).',
            );

            // Clear the mock data lists as API data is now the source of truth.
            // The getters will derive featured/nearby from _apiVillas.
            _featuredVillas = [];
            _nearbyVillas = [];
          } catch (e) {
            sl.logger.e('Error processing villas from dashboard data: $e');
            _error = 'Error processing villa data from dashboard.';
          }
        } else {
          _error ??=
              'Failed to process dashboard data.'; // Set if not already set by specific condition
        }
      }
    } catch (e) {
      _error = 'Failed to load dashboard data: ${e.toString()}';
      sl.logger.e('Error fetching dashboard data: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Fetch categories from the API
  Future<void> fetchCategories() async {
    _isCategoriesLoading = true;
    _categoriesError = null;
    notifyListeners();

    try {
      final response = await _apiService.getCategories();
      sl.logger.d('Categories API response: $response');
      sl.logger.d('Response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> categoryList;

        // Handle different response structures
        if (response is List) {
          categoryList = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            categoryList = response['data'];
            sl.logger.d(
              'Using response["data"] with ${categoryList.length} items',
            );
          } else if (response['categories'] != null &&
              response['categories'] is List) {
            categoryList = response['categories'];
            sl.logger.d(
              'Using response["categories"] with ${categoryList.length} items',
            );
          } else {
            // If response is a map but no recognized list field, treat whole response as error
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        _categories =
            categoryList
                .map((json) {
                  try {
                    return Category.fromJson(json);
                  } catch (e) {
                    sl.logger.w('Failed to parse category: $json, error: $e');
                    return null;
                  }
                })
                .where((category) => category != null)
                .cast<Category>()
                .toList();

        // Add default "All" category at the beginning
        _categories.insert(
          0,
          Category(id: 0, enabled: true, isDeleted: false, title: 'All Villas'),
        );

        // Set the first category as selected by default
        if (_categories.isNotEmpty && _selectedCategory == null) {
          _selectedCategory = _categories.first;
        }
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      _categoriesError = 'Failed to load categories: ${e.toString()}';
      sl.logger.e('Error fetching categories: $e');

      // Fallback to default categories in case of error
      _categories = [
        Category(id: 0, enabled: true, isDeleted: false, title: 'All Villas'),
        Category(id: 1, enabled: true, isDeleted: false, title: 'Beachfront'),
        Category(
          id: 2,
          enabled: true,
          isDeleted: false,
          title: 'Mountain View',
        ),
        Category(id: 3, enabled: true, isDeleted: false, title: 'City Center'),
      ];
      _selectedCategory = _categories.first;
    } finally {
      _isCategoriesLoading = false;
      notifyListeners();
    }
  }

  /// Set user provider reference
  void setUserProvider(UserProvider userProvider) {
    _userProvider = userProvider;
  }

  /// Select a category and refresh data
  void selectCategory(Category category) {
    _selectedCategory = category;
    notifyListeners();

    // Refresh dashboard data with new category
    _refreshDashboardDataForCategory();
  }

  /// Private method to refresh dashboard data when category changes
  Future<void> _refreshDashboardDataForCategory() async {
    if (_selectedCategory == null) return;

    try {
      // Get dynamic parameters
      final position = await LocationService.getCachedOrCurrentLocation();
      final dateRange = app_date_utils.DateUtils.getDashboardDateRange();
      final userId = _userProvider?.currentUser?.id ?? 1;

      await fetchDashboardData(
        lat: LocationService.latitudeToString(position.latitude),
        long: LocationService.longitudeToString(position.longitude),
        userId: userId,
        fromDate: dateRange['fromDate']!,
        toDate: dateRange['toDate']!,
        radius: 300, // Default radius in km
        categoryId: _selectedCategory!.id == 0 ? null : _selectedCategory!.id,
      );
    } catch (e) {
      sl.logger.e('Error refreshing dashboard data for category: $e');
    }
  }

  /// Get villas filtered by selected category
  List<Villa> getFilteredVillas() {
    if (_selectedCategory == null || _selectedCategory!.id == 0) {
      // Return all villas for "All" category
      return allVillas;
    }

    // Filter villas by category (this would depend on your Villa model having a categoryId)
    // For now, returning all villas - you may need to update Villa model to include categoryId
    return allVillas;
  }

  /// Fetch amenities from the API
  Future<void> fetchAmenities() async {
    _isAmenitiesLoading = true;
    _amenitiesError = null;
    notifyListeners();

    try {
      final response = await _apiService.getAmenitiesMaster();
      sl.logger.d('Amenities API response: $response');
      sl.logger.d('Response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> amenitiesList;

        // Handle different response structures
        if (response is List) {
          amenitiesList = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            amenitiesList = response['data'];
            sl.logger.d(
              'Using response["data"] with ${amenitiesList.length} items',
            );
          } else if (response['amenities'] != null &&
              response['amenities'] is List) {
            amenitiesList = response['amenities'];
            sl.logger.d(
              'Using response["amenities"] with ${amenitiesList.length} items',
            );
          } else {
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        _amenities =
            amenitiesList
                .map((json) {
                  try {
                    return Amenity.fromJson(json);
                  } catch (e) {
                    sl.logger.w('Failed to parse amenity: $json, error: $e');
                    return null;
                  }
                })
                .where((amenity) => amenity != null && amenity.isActive)
                .cast<Amenity>()
                .toList();

        sl.logger.d('Successfully parsed ${_amenities.length} amenities');
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      _amenitiesError = 'Failed to load amenities: ${e.toString()}';
      sl.logger.e('Error fetching amenities: $e');
    } finally {
      _isAmenitiesLoading = false;
      notifyListeners();
    }
  }

  /// Initialize provider data with dynamic parameters
  Future<void> initialize() async {
    try {
      sl.logger.d('VillaProvider: Starting initialization');

      // First load categories and amenities
      await fetchCategories();
      await fetchAmenities();

      // Load API villas data (prioritize over mock data)
      await fetchAllVillas();

      // Load wishlist data
      await fetchWishlist();

      // Then load dashboard data with dynamic parameters
      await _loadDashboardDataWithDynamicParams();

      sl.logger.d(
        'VillaProvider: Initialization completed. _apiVillas count: ${_apiVillas.length}',
      );
    } catch (e) {
      sl.logger.e('VillaProvider: Error initializing villa provider: $e');
    }
  }

  /// Load dashboard data with all dynamic parameters
  Future<void> _loadDashboardDataWithDynamicParams() async {
    try {
      // Get current location
      final position = await LocationService.getCachedOrCurrentLocation();

      // Get date range (today to next 7 days)
      final dateRange = app_date_utils.DateUtils.getDashboardDateRange();

      // Get current user ID
      final userId = _userProvider?.currentUser?.id ?? 1;

      // Get selected category ID (default to first category if none selected)
      final selectedCatId =
          _selectedCategory?.id ??
          (_categories.isNotEmpty ? _categories.first.id : 1);
      final int? categoryIdToSend = selectedCatId == 0 ? null : selectedCatId;

      sl.logger.d(
        'Loading dashboard with dynamic params: '
        'lat=${position.latitude}, long=${position.longitude}, '
        'userId=$userId, fromDate=${dateRange['fromDate']}, '
        'toDate=${dateRange['toDate']}, categoryId=$categoryIdToSend',
      );

      await fetchDashboardData(
        lat: LocationService.latitudeToString(position.latitude),
        long: LocationService.longitudeToString(position.longitude),
        userId: userId,
        fromDate: dateRange['fromDate']!,
        toDate: dateRange['toDate']!,
        radius: 300, // Default radius in km
        categoryId: categoryIdToSend,
      );
    } catch (e) {
      sl.logger.e('Error loading dashboard data with dynamic params: $e');
      _error = 'Failed to load villa data: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Refresh all data (useful for pull-to-refresh)
  Future<void> refreshData() async {
    sl.logger.d('VillaProvider: Refreshing data');
    await initialize();
  }

  /// Debug method to log current state
  void debugLogState() {
    sl.logger.d('VillaProvider: Current state:');
    sl.logger.d('  - _apiVillas count: ${_apiVillas.length}');
    sl.logger.d('  - _featuredVillas count: ${_featuredVillas.length}');
    sl.logger.d('  - _nearbyVillas count: ${_nearbyVillas.length}');
    sl.logger.d('  - isLoading: $_isLoading');
    sl.logger.d('  - error: $_error');

    if (_apiVillas.isNotEmpty) {
      sl.logger.d(
        '  - First 3 API villa IDs: ${_apiVillas.take(3).map((v) => v.id).toList()}',
      );
    }
  }

  /// Test API connectivity and response structure
  Future<void> testApiResponses() async {
    sl.logger.i('Testing API connectivity...');

    try {
      // Test categories endpoint
      sl.logger.i('Testing categories endpoint...');
      final categoriesResponse = await _apiService.getCategories();
      sl.logger.i('Categories response: $categoriesResponse');
      sl.logger.i(
        'Categories response type: ${categoriesResponse.runtimeType}',
      );

      // Test dashboard endpoint
      sl.logger.i('Testing dashboard endpoint...');
      final dashboardResponse = await _apiService.getDashboardData(
        lat: "19.281112",
        long: "73.047047",
        userId: 1,
        fromDate: "2025-06-06",
        toDate: "2025-06-09",
        radius: 300,
        categoryId:
            1, // Assuming for test purposes, we always send a categoryId or a specific one.
      );
      sl.logger.i('Dashboard response: $dashboardResponse');
      sl.logger.i('Dashboard response type: ${dashboardResponse.runtimeType}');
    } catch (e) {
      sl.logger.e('API test failed: $e');
    }
  }

  /// Fetch all villas from /villa endpoint
  Future<void> fetchAllVillas() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      sl.logger.d('Fetching villas from /villa endpoint');
      final response = await _apiService.getAllVillas();

      sl.logger.d('Villa API response: $response');
      sl.logger.d('Villa response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> villaList;

        // Handle different response structures
        if (response is List) {
          villaList = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            villaList = response['data'];
            sl.logger.d(
              'Using response["data"] with ${villaList.length} items',
            );
          } else if (response['villas'] != null && response['villas'] is List) {
            villaList = response['villas'];
            sl.logger.d(
              'Using response["villas"] with ${villaList.length} items',
            );
          } else {
            // If response is a map but no recognized list field, treat whole response as error
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        _apiVillas =
            villaList
                .map((json) {
                  try {
                    if (json is! Map<String, dynamic>) {
                      sl.logger.w(
                        'Villa JSON is not a Map: ${json.runtimeType} - $json',
                      );
                      return null;
                    }

                    // Log the villa being parsed for debugging
                    final villaId = json['id'];
                    final villaName = json['name'];
                    sl.logger.d('Parsing villa: ID=$villaId, Name=$villaName');

                    return ApiVilla.fromJsonSafe(json);
                  } catch (e, stackTrace) {
                    sl.logger.e('Failed to parse villa:');
                    sl.logger.e(ApiVilla.debugJsonData(json));
                    sl.logger.e('Error: $e');
                    sl.logger.e('Stack trace: $stackTrace');
                    return null;
                  }
                })
                .where((villa) => villa != null)
                .cast<ApiVilla>()
                .toList();

        sl.logger.d('Successfully parsed ${_apiVillas.length} villas');
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      _error = 'Failed to load villas: ${e.toString()}';
      sl.logger.e('Error fetching villas: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Fetch user's wishlist
  Future<void> fetchWishlist() async {
    final userId = _userProvider?.currentUser?.id;
    if (userId == null) {
      sl.logger.w('Cannot fetch wishlist: User not logged in');
      return;
    }

    _isWishlistLoading = true;
    _wishlistError = null;
    notifyListeners();

    try {
      sl.logger.d('Fetching wishlist for user ID: $userId');
      final response = await _apiService.getWishlist(userId: userId);

      sl.logger.d('Wishlist API response: $response');
      sl.logger.d('Wishlist response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> wishlistData;

        // Handle different response structures
        if (response is List) {
          wishlistData = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            wishlistData = response['data'];
            sl.logger.d(
              'Using response["data"] with ${wishlistData.length} items',
            );
          } else {
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        _wishlistItems =
            wishlistData
                .map((json) {
                  try {
                    if (json is! Map<String, dynamic>) {
                      sl.logger.w(
                        'Wishlist JSON is not a Map: ${json.runtimeType} - $json',
                      );
                      return null;
                    }
                    return WishlistItem.fromJsonSafe(json);
                  } catch (e, stackTrace) {
                    sl.logger.e('Failed to parse wishlist item: $json');
                    sl.logger.e('Error: $e');
                    sl.logger.e('Stack trace: $stackTrace');
                    return null;
                  }
                })
                .where((item) => item != null)
                .cast<WishlistItem>()
                .toList();

        sl.logger.d(
          'Successfully parsed ${_wishlistItems.length} wishlist items',
        );
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      _wishlistError = 'Failed to load wishlist: ${e.toString()}';
      sl.logger.e('Error fetching wishlist: $e');
    } finally {
      _isWishlistLoading = false;
      notifyListeners();
    }
  }

  /// Add villa to wishlist
  Future<void> addToWishlist(String villaId) async {
    final userId = _userProvider?.currentUser?.id;
    if (userId == null) {
      sl.logger.w('Cannot add to wishlist: User not logged in');
      return;
    }

    try {
      sl.logger.d('Adding villa $villaId to wishlist for user $userId');

      final request = AddToWishlistRequest(
        villaId: int.parse(villaId),
        userId: userId,
      );

      final response = await _apiService.addToWishlist(request.toJson());
      sl.logger.d('Add to wishlist response: $response');

      // Refresh wishlist to get updated data
      await fetchWishlist();
    } catch (e) {
      sl.logger.e('Error adding to wishlist: $e');
      _wishlistError = 'Failed to add to wishlist: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Remove villa from wishlist
  Future<void> removeFromWishlist(String villaId) async {
    final userId = _userProvider?.currentUser?.id;
    if (userId == null) {
      sl.logger.w('Cannot remove from wishlist: User not logged in');
      _wishlistError =
          'User not logged in. Please log in to modify your wishlist.';
      notifyListeners();
      return;
    }

    try {
      sl.logger.d(
        'Attempting to remove villa $villaId from wishlist for user $userId',
      );

      // Prepare the same payload structure as for adding to wishlist
      final Map<String, dynamic> wishlistData = {
        'villaId': int.parse(villaId), // Ensure villaId is an int
        'userId': userId,
      };

      // Call the updated ApiService method which now expects a Map
      final response = await _apiService.removeFromWishlist(wishlistData);
      sl.logger.d('Remove from wishlist response: $response');

      // Refresh wishlist to get updated data
      await fetchWishlist();
    } catch (e) {
      sl.logger.e('Error removing from wishlist: $e');
      _wishlistError = 'Failed to remove from wishlist: ${e.toString()}';
      notifyListeners();
    }
  }

  /// Initialize wishlist data
  Future<void> initializeWishlist() async {
    await fetchWishlist();
  }

  /// Fetch villa details by ID from the API
  Future<ApiVilla?> fetchVillaDetailsById(String villaId) async {
    _isVillaDetailLoading = true;
    _villaDetailError = null;
    _currentVillaDetail = null; // Clear previous detail
    notifyListeners();

    try {
      sl.logger.d('Fetching villa details for ID: $villaId');
      final response = await _apiService.getVillaDetails(villaId);
      sl.logger.d('Villa details API response: $response');
      sl.logger.d('Villa details response type: ${response.runtimeType}');

      if (response != null && response is Map<String, dynamic>) {
        // Check if the response has a 'data' field and if it's a map
        if (response.containsKey('data') &&
            response['data'] is Map<String, dynamic>) {
          _currentVillaDetail = ApiVilla.fromJsonSafe(
            response['data'] as Map<String, dynamic>,
          );
          sl.logger.d(
            'Successfully parsed villa detail from response["data"] for ID: $villaId',
          );
        } else {
          // If no 'data' field, or 'data' is not a map, try parsing the response directly
          _currentVillaDetail = ApiVilla.fromJsonSafe(response);
          sl.logger.d(
            'Successfully parsed villa detail directly from response for ID: $villaId',
          );
        }

        // Optionally, update the _apiVillas list if the fetched villa is newer or not present
        if (_currentVillaDetail != null) {
          final index = _apiVillas.indexWhere(
            (v) => v.id == _currentVillaDetail!.id,
          );
          if (index != -1) {
            _apiVillas[index] = _currentVillaDetail!;
          } else {
            _apiVillas.add(_currentVillaDetail!);
          }
        }
        _isVillaDetailLoading = false;
        notifyListeners();
        return _currentVillaDetail;
      } else {
        throw Exception(
          'Invalid or empty response from server for villa details.',
        );
      }
    } catch (e, stackTrace) {
      _villaDetailError =
          'Failed to load villa details for ID $villaId: ${e.toString()}';
      sl.logger.e('Error fetching villa details for ID $villaId: $e');
      sl.logger.e('Stack trace: $stackTrace');
      _isVillaDetailLoading = false;
      notifyListeners();
      return null;
    }
  }

  /// Perform advance search with filters - results stored separately for map
  Future<void> performAdvanceSearch(Map<String, dynamic> searchData) async {
    // Removed global _isLoading and _error modifications
    // notifyListeners(); // Initial notifyListeners removed as no global state changed here that HomeScreen cares about immediately

    String? localOperationError; // For logging or specific feedback if needed

    try {
      sl.logger.d('Performing advance search with data: $searchData');
      final response = await _apiService.advanceSearch(searchData);

      sl.logger.d('Advance search API response: $response');
      sl.logger.d('Advance search response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> villaList;

        // Handle different response structures
        if (response is List) {
          villaList = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            villaList = response['data'];
            sl.logger.d(
              'Using response["data"] with ${villaList.length} items',
            );
          } else if (response['villas'] != null && response['villas'] is List) {
            villaList = response['villas'];
            sl.logger.d(
              'Using response["villas"] with ${villaList.length} items',
            );
          } else {
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        // Store search results in separate list to avoid affecting home screen
        _mapSearchResults =
            villaList
                .map((json) {
                  try {
                    if (json is! Map<String, dynamic>) {
                      sl.logger.w(
                        'Villa JSON is not a Map: ${json.runtimeType} - $json',
                      );
                      return null;
                    }

                    return ApiVilla.fromJsonSafe(json);
                  } catch (e, stackTrace) {
                    sl.logger.e('Failed to parse villa from advance search:');
                    sl.logger.e(ApiVilla.debugJsonData(json));
                    sl.logger.e('Error: $e');
                    sl.logger.e('Stack trace: $stackTrace');
                    return null;
                  }
                })
                .where((villa) => villa != null)
                .cast<ApiVilla>()
                .toList();

        sl.logger.d(
          'Successfully parsed ${_mapSearchResults.length} villas from advance search',
        );
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      localOperationError = 'Failed to perform advance search: ${e.toString()}';
      sl.logger.e('Error performing advance search: $e');
      _mapSearchResults = []; // Clear results on error for this operation
    } finally {
      // Removed global _isLoading modification
      notifyListeners(); // Notify about changes to _mapSearchResults
    }
  }

  /// Perform search with filters for map - stores results separately
  Future<void> performMapSearch(Map<String, dynamic> searchData) async {
    // Removed global _isLoading and _error modifications
    // notifyListeners(); // Initial notifyListeners removed

    String? localOperationError; // For logging or specific feedback if needed

    try {
      sl.logger.d('Performing map search with data: $searchData');
      final response = await _apiService.advanceSearch(searchData);

      sl.logger.d('Map search API response: $response');
      sl.logger.d('Map search response type: ${response.runtimeType}');

      if (response != null) {
        List<dynamic> villaList;

        // Handle different response structures
        if (response is List) {
          villaList = response;
          sl.logger.d('Response is a List with ${response.length} items');
        } else if (response is Map<String, dynamic>) {
          sl.logger.d('Response is a Map with keys: ${response.keys.toList()}');
          if (response['data'] != null && response['data'] is List) {
            villaList = response['data'];
            sl.logger.d(
              'Using response["data"] with ${villaList.length} items',
            );
          } else if (response['villas'] != null && response['villas'] is List) {
            villaList = response['villas'];
            sl.logger.d(
              'Using response["villas"] with ${villaList.length} items',
            );
          } else {
            sl.logger.e(
              'Invalid response format - no valid list found in: ${response.toString()}',
            );
            throw Exception('Invalid response format: ${response.toString()}');
          }
        } else {
          sl.logger.e('Unexpected response type: ${response.runtimeType}');
          throw Exception('Unexpected response type: ${response.runtimeType}');
        }

        // Store search results in separate list for map usage
        _mapSearchResults =
            villaList
                .map((json) {
                  try {
                    if (json is! Map<String, dynamic>) {
                      sl.logger.w(
                        'Villa JSON is not a Map: ${json.runtimeType} - $json',
                      );
                      return null;
                    }

                    return ApiVilla.fromJsonSafe(json);
                  } catch (e, stackTrace) {
                    sl.logger.e('Failed to parse villa from map search:');
                    sl.logger.e(ApiVilla.debugJsonData(json));
                    sl.logger.e('Error: $e');
                    sl.logger.e('Stack trace: $stackTrace');
                    return null;
                  }
                })
                .where((villa) => villa != null)
                .cast<ApiVilla>()
                .toList();

        sl.logger.d(
          'Successfully parsed ${_mapSearchResults.length} villas from map search',
        );
      } else {
        throw Exception('Empty response from server');
      }
    } catch (e) {
      localOperationError = 'Failed to perform map search: ${e.toString()}';
      sl.logger.e('Error performing map search: $e');
      _mapSearchResults = []; // Clear results on error for this operation
    } finally {
      // Removed global _isLoading modification
      notifyListeners(); // Notify about changes to _mapSearchResults
    }
  }

  /// Clear map search results and reset to show all villas on map
  void clearMapSearchResults() {
    _mapSearchResults = [];
    notifyListeners();
  }

  /// Set map search results directly (for local filtering)
  void setMapSearchResults(List<ApiVilla> results) {
    _mapSearchResults = results;
    notifyListeners();
  }
}
